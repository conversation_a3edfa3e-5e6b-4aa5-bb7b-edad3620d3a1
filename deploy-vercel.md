# Vercel Deployment Guide

## Quick Fix for Dependency Issues

If you encounter dependency resolution errors during Vercel deployment, try these solutions:

### Option 1: Use the Updated Configuration

The project now includes:
- `.npmrc` with `legacy-peer-deps=true`
- `vercel.json` with optimized install command
- React 18.3.1 for better compatibility

### Option 2: Manual Deployment Steps

If automatic deployment fails:

1. **Clone and build locally first:**
   ```bash
   git clone your-repo-url
   cd your-project
   npm install --legacy-peer-deps
   npm run build
   ```

2. **If build succeeds locally, push to GitHub and try Vercel again**

3. **Alternative: Use Vercel CLI**
   ```bash
   npm i -g vercel
   vercel login
   vercel --prod
   ```

### Option 3: Environment Variables Override

In Vercel dashboard, add these build environment variables:
- `NPM_FLAGS=--legacy-peer-deps`
- `NODE_OPTIONS=--max-old-space-size=4096`

### Option 4: Package.json Scripts Override

If needed, you can temporarily modify package.json scripts in Vercel:

```json
{
  "scripts": {
    "build": "npm install --legacy-peer-deps && next build",
    "vercel-build": "npm install --legacy-peer-deps && next build"
  }
}
```

## Dependency Versions (Tested & Working)

```json
{
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "@types/react": "^18.3.12",
  "@types/react-dom": "^18.3.1",
  "react-day-picker": "8.10.1",
  "@livekit/components-react": "^2.9.9",
  "livekit-client": "^2.13.3",
  "livekit-server-sdk": "^2.13.0"
}
```

## Troubleshooting

If deployment still fails:

1. **Check Vercel build logs** for specific error messages
2. **Verify all environment variables** are set correctly
3. **Ensure backend API** is accessible from Vercel's servers
4. **Test API endpoints** manually to confirm they work

## Support

If you continue to have issues, the project is configured to work with:
- Node.js 18+
- npm with legacy peer deps
- React 18 ecosystem

The build has been tested and works locally with these configurations.
