# Medical AI Frontend

A Next.js-based frontend application for a medical AI chatbot with voice interaction capabilities.

## Features

- 🤖 AI-powered medical consultations
- 🎤 Voice chat with real-time transcription
- 💬 Text-based conversations
- 👤 User authentication and profiles
- 🎨 Theme customization (light/dark/system)
- 📱 Responsive design

## Tech Stack

- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Voice**: LiveKit
- **State Management**: React Hooks
- **Authentication**: Custom API integration

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or pnpm
- Backend API server running

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env.local
   ```

4. Configure your environment variables in `.env.local`

5. Run the development server:
   ```bash
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Environment Variables

Required environment variables (see `.env.example`):

- `OPENAI_API_KEY` - OpenAI API key
- `NEXT_PUBLIC_LIVEKIT_URL` - LiveKit WebSocket URL
- `LIVEKIT_API_KEY` - LiveKit API key
- `LIVEKIT_API_SECRET` - LiveKit API secret
- `NEXT_PUBLIC_BACKEND_URL` - Backend API URL
- `DEEPGRAM_API_KEY` - Deepgram API key (optional)

## Deployment on Vercel

### Automatic Deployment

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push

### Manual Deployment

1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```

2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy:
   ```bash
   vercel --prod
   ```

### Environment Variables for Vercel

Set these in your Vercel project settings:

- `OPENAI_API_KEY`
- `NEXT_PUBLIC_LIVEKIT_URL`
- `LIVEKIT_API_KEY`
- `LIVEKIT_API_SECRET`
- `NEXT_PUBLIC_BACKEND_URL`
- `DEEPGRAM_API_KEY`

## Build Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── chat/              # Chat pages
│   ├── login/             # Authentication pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # UI components
│   └── settings/         # Settings components
├── lib/                  # Utility libraries
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── public/               # Static assets
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is private and proprietary.
